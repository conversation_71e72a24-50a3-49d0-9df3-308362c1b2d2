<!-- WebSocket 连接状态提示组件 -->
<template>
  <div
    v-if="shouldShowStatus"
    class="py-1 mx-3 mb-2 rounded-md text-xs transition-all duration-300"
    :class="statusClasses"
  >
    <div class="flex items-center gap-2">
      <!-- 状态图标 -->
      <div class="flex-shrink-0">
        <div
          v-if="status === 'connecting' || status === 'reconnecting'"
          class="w-4 h-4 border-current border-t-transparent rounded-full animate-spin"
        ></div>
        <div v-else-if="status === 'connected'" class="w-4 h-4 bg-green-500 rounded-full"></div>
        <div v-else-if="status === 'disconnected'" class="w-4 h-4 bg-yellow-500 rounded-full"></div>
        <div v-else-if="status === 'error'" class="w-4 h-4 bg-red-500 rounded-full"></div>
      </div>

      <!-- 状态文本 -->
      <span class="flex-1">{{ statusText }}</span>

      <!-- 关闭按钮 (仅在连接成功时显示) -->
      <button
        v-if="status === 'connected' && canClose"
        @click="handleClose"
        class="flex-shrink-0 ml-2 p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors"
        title="关闭提示"
      >
        <svg class="w-3 h-3" viewBox="0 0 1024 1024" fill="currentColor">
          <path
            d="M562.9 517.7l173.1 173c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0l-173-173.1c-3.1-3.1-8.2-3.1-11.3 0L333.3 736c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l173.1-173.1c3.1-3.1 3.1-8.2 0-11.3L288 333.3c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l173.1 173.1c3.1 3.1 8.2 3.1 11.3 0l173-173.1c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3l-173.1 173a8.15 8.15 0 0 0 0 11.4z"
          ></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useWebSocketStore } from '../store/websocket'
import { wsService, WebSocketState } from '../services/websocketService'

// 使用全局 WebSocket 状态
const webSocketStore = useWebSocketStore()

// 从 store 中获取所有状态和计算属性
const {
  state: status,
  reconnectAttempts,
  statusText,
  statusClasses,
  shouldShowStatus,
  canClose,
  isManuallyHidden,
  setManuallyHidden
} = webSocketStore

// 简化的方法
const handleClose = () => {
  console.log('WebSocketStatus - 用户点击关闭按钮')
  setManuallyHidden(true)
}

// 初始化同步（确保组件挂载时状态正确）
const initializeStatus = async () => {
  console.log('WebSocketStatus - 初始化状态同步')
  try {
    // 从 WebSocket 服务同步状态到全局 store
    webSocketStore.syncFromWebSocketService(wsService)
  } catch (error) {
    console.warn('WebSocketStatus - 初始化状态同步失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  console.log('WebSocketStatus - 组件挂载，使用全局状态')

  // 初始化状态同步
  await initializeStatus()

  console.log('WebSocketStatus - 全局状态已同步，当前状态:', {
    status: status.value,
    reconnectAttempts: reconnectAttempts.value,
    shouldShow: shouldShowStatus.value
  })
})

onUnmounted(() => {
  console.log('WebSocketStatus - 组件卸载')
  // 使用全局状态后，不需要手动清理
})
</script>

<style scoped>
/* 动画效果 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 旋转动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
