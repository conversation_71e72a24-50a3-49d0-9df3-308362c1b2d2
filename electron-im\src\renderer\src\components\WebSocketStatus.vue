<!-- WebSocket 连接状态提示组件 -->
<template>
  <div
    v-if="shouldShowStatus"
    class="py-1 mx-3 mb-2 rounded-md text-xs transition-all duration-300"
    :class="statusClasses"
  >
    <div class="flex items-center gap-2">
      <!-- 状态图标 -->
      <div class="flex-shrink-0">
        <div
          v-if="status === 'connecting' || status === 'reconnecting'"
          class="w-4 h-4 border-current border-t-transparent rounded-full animate-spin"
        ></div>
        <div v-else-if="status === 'connected'" class="w-4 h-4 bg-green-500 rounded-full"></div>
        <div v-else-if="status === 'disconnected'" class="w-4 h-4 bg-yellow-500 rounded-full"></div>
        <div v-else-if="status === 'error'" class="w-4 h-4 bg-red-500 rounded-full"></div>
      </div>

      <!-- 状态文本 -->
      <span class="flex-1">{{ statusText }}</span>

      <!-- 关闭按钮 (仅在连接成功时显示) -->
      <button
        v-if="status === 'connected' && canClose"
        @click="handleClose"
        class="flex-shrink-0 ml-2 p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors"
        title="关闭提示"
      >
        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path
            fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { wsService, WebSocketState } from '../services/websocketService'

// 组件状态
const status = ref<WebSocketState>(WebSocketState.DISCONNECTED)
const reconnectAttempts = ref(0)
const maxReconnectAttempts = ref(5)
const canClose = ref(false)
const isManuallyHidden = ref(false)

// 计算属性
const shouldShowStatus = computed(() => {
  // 调试：总是显示状态，方便查看问题
  console.log(
    'WebSocketStatus - 当前状态:',
    status.value,
    '重连次数:',
    reconnectAttempts.value,
    '手动隐藏:',
    isManuallyHidden.value
  )

  // 如果手动隐藏了，只在非连接状态时显示
  if (isManuallyHidden.value) {
    return status.value !== WebSocketState.CONNECTED
  }

  // 显示状态的条件：
  // 1. 正在连接
  // 2. 正在重连
  // 3. 连接错误
  // 4. 连接成功但曾经重连过（表示是重连成功）
  // 5. 连接断开
  const hasEverReconnected = wsService.getHasEverReconnected()
  return (
    status.value === WebSocketState.CONNECTING ||
    status.value === WebSocketState.RECONNECTING ||
    status.value === WebSocketState.ERROR ||
    status.value === WebSocketState.DISCONNECTED ||
    (status.value === WebSocketState.CONNECTED &&
      (hasEverReconnected || reconnectAttempts.value > 0))
  )
})

const statusText = computed(() => {
  const hasEverReconnected = wsService.getHasEverReconnected()

  switch (status.value) {
    case WebSocketState.CONNECTING:
      return hasEverReconnected || reconnectAttempts.value > 0 ? '正在重新连接...' : '正在连接...'
    case WebSocketState.CONNECTED:
      return hasEverReconnected || reconnectAttempts.value > 0 ? '已恢复实时连接' : '连接成功'
    case WebSocketState.DISCONNECTED:
      return hasEverReconnected || reconnectAttempts.value > 0
        ? '连接已断开，正在尝试重新连接...'
        : '连接已断开'
    case WebSocketState.RECONNECTING:
      return `正在尝试第 ${reconnectAttempts.value} 次重连...`
    case WebSocketState.ERROR:
      return reconnectAttempts.value >= maxReconnectAttempts.value
        ? `连接失败，已尝试 ${maxReconnectAttempts.value} 次重连`
        : '连接错误'
    default:
      return '未知状态'
  }
})

const statusClasses = computed(() => {
  switch (status.value) {
    case WebSocketState.CONNECTING:
    case WebSocketState.RECONNECTING:
      return `bg-blue-50 text-blue-700`
    case WebSocketState.CONNECTED:
      return `bg-green-50 text-green-700`
    case WebSocketState.DISCONNECTED:
      return reconnectAttempts.value > 0
        ? `bg-yellow-50  text-yellow-700`
        : `bg-gray-50 text-gray-700`
    case WebSocketState.ERROR:
      return ` bg-red-50 text-red-700`
    default:
      return `bg-gray-50 text-gray-700`
  }
})

// 方法
const handleClose = () => {
  isManuallyHidden.value = true
  canClose.value = false
}

const updateStatus = () => {
  const newStatus = wsService.getState()
  const newReconnectAttempts = wsService.getReconnectAttempts()
  const hasEverReconnected = wsService.getHasEverReconnected()

  console.log('WebSocketStatus - 更新状态:', {
    oldStatus: status.value,
    newStatus,
    oldReconnectAttempts: reconnectAttempts.value,
    newReconnectAttempts,
    hasEverReconnected
  })

  const wasReconnecting =
    status.value === WebSocketState.RECONNECTING || reconnectAttempts.value > 0

  status.value = newStatus
  reconnectAttempts.value = newReconnectAttempts

  // 连接成功后的处理
  if (newStatus === WebSocketState.CONNECTED) {
    // 如果曾经重连过或者之前是重连状态，显示关闭按钮
    if (hasEverReconnected || wasReconnecting || newReconnectAttempts > 0) {
      setTimeout(() => {
        canClose.value = true
      }, 2000) // 2秒后显示关闭按钮
    } else {
      // 首次连接成功，也显示关闭按钮，但延迟更短
      setTimeout(() => {
        canClose.value = true
      }, 1000) // 1秒后显示关闭按钮
    }
  }

  // 如果状态变为非连接状态，重置手动隐藏标志
  if (newStatus !== WebSocketState.CONNECTED) {
    isManuallyHidden.value = false
    canClose.value = false
  }
}

// 监听 WebSocket 状态变化
const handleStateChange = (newState: WebSocketState) => {
  updateStatus()
}

// 生命周期
onMounted(() => {
  // 初始化状态
  updateStatus()

  // 监听状态变化
  wsService.on('onStateChange', handleStateChange)
})

onUnmounted(() => {
  // 清理事件监听
  wsService.off('onStateChange')
})

// 监听重连次数变化
watch(
  () => wsService.getReconnectAttempts(),
  () => {
    updateStatus()
  }
)
</script>

<style scoped>
/* 动画效果 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 旋转动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
