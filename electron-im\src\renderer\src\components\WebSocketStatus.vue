<!-- WebSocket 连接状态提示组件 -->
<template>
  <div
    v-if="shouldShowStatus"
    class="py-1 mx-3 mb-2 rounded-md text-xs transition-all duration-300"
    :class="statusClasses"
  >
    <div class="flex items-center gap-2">
      <!-- 状态图标 -->
      <div class="flex-shrink-0">
        <div
          v-if="status === WebSocketState.CONNECTING || status === WebSocketState.RECONNECTING"
          class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"
        ></div>
        <div
          v-else-if="status === WebSocketState.CONNECTED"
          class="w-4 h-4 bg-green-500 rounded-full"
        ></div>
        <div
          v-else-if="status === WebSocketState.DISCONNECTED"
          class="w-4 h-4 bg-yellow-500 rounded-full"
        ></div>
        <div
          v-else-if="status === WebSocketState.ERROR"
          class="w-4 h-4 bg-red-500 rounded-full"
        ></div>
      </div>

      <!-- 状态文本 -->
      <span class="flex-1">{{ statusText }}</span>

      <!-- 关闭按钮 (仅在连接成功时显示) -->
      <button
        v-if="status === WebSocketState.CONNECTED && canClose"
        @click="handleClose"
        class="flex-shrink-0 ml-2 p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors"
        title="关闭提示"
      >
        <svg class="w-3 h-3" viewBox="0 0 1024 1024" fill="currentColor">
          <path
            d="M562.9 517.7l173.1 173c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0l-173-173.1c-3.1-3.1-8.2-3.1-11.3 0L333.3 736c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l173.1-173.1c3.1-3.1 3.1-8.2 0-11.3L288 333.3c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l173.1 173.1c3.1 3.1 8.2 3.1 11.3 0l173-173.1c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3l-173.1 173a8.15 8.15 0 0 0 0 11.4z"
          ></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useWebSocketStore } from '../store/websocket'
import { wsService, WebSocketState } from '../services/websocketService'

// 使用全局 WebSocket 状态
const webSocketStore = useWebSocketStore()

// 从 store 中获取所有状态和计算属性
const {
  state: status,
  reconnectAttempts,
  statusText,
  statusClasses,
  shouldShowStatus,
  canClose,
  isManuallyHidden,
  setManuallyHidden
} = webSocketStore

// 简化的方法
const handleClose = () => {
  console.log('WebSocketStatus - 用户点击关闭按钮')
  setManuallyHidden(true)
}

// 状态同步处理
const handleStateChange = (newState: WebSocketState) => {
  console.log('WebSocketStatus - 收到状态变化事件:', newState)
  // 强制同步状态到全局 store
  webSocketStore.syncFromWebSocketService(wsService)
}

// 初始化同步（确保组件挂载时状态正确）
const initializeStatus = () => {
  console.log('WebSocketStatus - 初始化状态同步')
  try {
    // 立即同步当前状态
    webSocketStore.syncFromWebSocketService(wsService)

    console.log('WebSocketStatus - 状态同步完成:', {
      wsServiceState: wsService.getState(),
      storeState: status.value,
      reconnectAttempts: reconnectAttempts.value,
      shouldShow: shouldShowStatus.value
    })
  } catch (error) {
    console.warn('WebSocketStatus - 初始化状态同步失败:', error)
  }
}

// 定期状态检查（确保状态一致性）
let statusCheckInterval: number | null = null

const startStatusCheck = () => {
  statusCheckInterval = window.setInterval(() => {
    const wsState = wsService.getState()
    if (wsState !== status.value) {
      console.log('WebSocketStatus - 检测到状态不一致，强制同步:', {
        wsServiceState: wsState,
        storeState: status.value
      })
      webSocketStore.syncFromWebSocketService(wsService)
    }
  }, 1000) // 每秒检查一次
}

const stopStatusCheck = () => {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval)
    statusCheckInterval = null
  }
}

// 生命周期
onMounted(() => {
  console.log('WebSocketStatus - 组件挂载，开始初始化')

  // 立即同步状态
  initializeStatus()

  // 监听 WebSocket 状态变化事件
  wsService.on('onStateChange', handleStateChange)

  // 开始定期状态检查
  startStatusCheck()

  console.log('WebSocketStatus - 初始化完成，当前状态:', {
    status: status.value,
    reconnectAttempts: reconnectAttempts.value,
    shouldShow: shouldShowStatus.value
  })
})

onUnmounted(() => {
  console.log('WebSocketStatus - 组件卸载，清理资源')

  // 移除事件监听
  wsService.off('onStateChange', handleStateChange)

  // 停止状态检查
  stopStatusCheck()
})
</script>

<style scoped>
/* 动画效果 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 旋转动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
